/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #667eea;
}

.logo i {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover,
.nav-link.active {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* Main Content */
.main {
    margin-top: 80px;
    padding: 2rem 0;
}

.section {
    display: none;
    min-height: calc(100vh - 80px);
}

.section.active {
    display: block;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    color: white;
}

.hero h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #ffd700;
}

.stat span {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Recent Uploads */
.recent-uploads {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin: 3rem 0;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.recent-uploads h3 {
    margin-bottom: 2rem;
    color: #333;
    font-size: 1.5rem;
}

/* File Grid */
.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.file-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #e0e0e0;
}

.file-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.file-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

.file-icon.pdf { color: #e74c3c; }
.file-icon.doc { color: #3498db; }
.file-icon.ppt { color: #f39c12; }
.file-icon.txt { color: #95a5a6; }

.file-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.file-subject {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.file-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #999;
}

.file-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.tag {
    background: #f8f9fa;
    color: #666;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
}

/* Upload Section */
.upload-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-top: 2rem;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #f8f9ff;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #5a67d8;
    background: #f0f4ff;
    transform: scale(1.02);
}

.upload-area i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.upload-area p {
    color: #666;
    font-size: 1.1rem;
}

#file-input {
    display: none;
}

.file-details {
    display: grid;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Filters */
.filters {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.filter-group select {
    padding: 0.5rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
}

/* Search */
.search-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.search-input-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

#search-input {
    flex: 1;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1.1rem;
}

#search-input:focus {
    outline: none;
    border-color: #667eea;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    font-size: 2rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 2rem;
}

#preview-content {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 1.5rem;
    text-align: center;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-actions {
    text-align: center;
    margin-bottom: 2rem;
}

.modal-footer {
    padding: 0 2rem 2rem;
}

.comments-section h4 {
    margin-bottom: 1rem;
    color: #333;
}

.comment {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
}

.comment-meta {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.add-comment {
    margin-top: 1rem;
}

.add-comment textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    margin-bottom: 1rem;
    resize: vertical;
    min-height: 80px;
}

/* Toast Notifications */
#toast-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 3000;
}

.toast {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 1rem;
    border-left: 4px solid #28a745;
    animation: slideIn 0.3s ease;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.warning {
    border-left-color: #ffc107;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        gap: 1rem;
    }
    
    .nav-link {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    
    .hero h2 {
        font-size: 2rem;
    }
    
    .hero-stats {
        grid-template-columns: 1fr;
    }
    
    .file-grid {
        grid-template-columns: 1fr;
    }
    
    .filters {
        grid-template-columns: 1fr;
    }
    
    .search-input-group {
        flex-direction: column;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .hero {
        padding: 2rem 0;
    }
    
    .hero h2 {
        font-size: 1.5rem;
    }
    
    .recent-uploads,
    .upload-form,
    .search-form {
        padding: 1rem;
    }
}
