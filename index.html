<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notes Sharing Platform</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-book-open"></i>
                <h1>NotesShare</h1>
            </div>
            <nav class="nav">
                <a href="#home" class="nav-link active" data-section="home">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="#upload" class="nav-link" data-section="upload">
                    <i class="fas fa-upload"></i> Upload
                </a>
                <a href="#browse" class="nav-link" data-section="browse">
                    <i class="fas fa-folder-open"></i> Browse
                </a>
                <a href="#search" class="nav-link" data-section="search">
                    <i class="fas fa-search"></i> Search
                </a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Home Section -->
        <section id="home" class="section active">
            <div class="container">
                <div class="hero">
                    <h2>Welcome to NotesShare</h2>
                    <p>Upload, share, and discover lecture notes and e-books with your community</p>
                    <div class="hero-stats">
                        <div class="stat">
                            <i class="fas fa-file-alt"></i>
                            <span id="total-files">0</span>
                            <label>Files Shared</label>
                        </div>
                        <div class="stat">
                            <i class="fas fa-graduation-cap"></i>
                            <span id="total-subjects">0</span>
                            <label>Subjects</label>
                        </div>
                        <div class="stat">
                            <i class="fas fa-comments"></i>
                            <span id="total-comments">0</span>
                            <label>Comments</label>
                        </div>
                    </div>
                </div>
                
                <div class="recent-uploads">
                    <h3>Recent Uploads</h3>
                    <div id="recent-files" class="file-grid">
                        <!-- Recent files will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Upload Section -->
        <section id="upload" class="section">
            <div class="container">
                <h2>Upload Notes or E-books</h2>
                <div class="upload-form">
                    <div class="upload-area" id="upload-area">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Drag and drop files here or click to browse</p>
                        <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.txt,.ppt,.pptx">
                    </div>
                    
                    <form id="file-form" class="file-details">
                        <div class="form-group">
                            <label for="file-title">Title:</label>
                            <input type="text" id="file-title" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-subject">Subject:</label>
                            <select id="file-subject" required>
                                <option value="">Select Subject</option>
                                <option value="Mathematics">Mathematics</option>
                                <option value="Physics">Physics</option>
                                <option value="Chemistry">Chemistry</option>
                                <option value="Biology">Biology</option>
                                <option value="Computer Science">Computer Science</option>
                                <option value="Engineering">Engineering</option>
                                <option value="Literature">Literature</option>
                                <option value="History">History</option>
                                <option value="Economics">Economics</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-description">Description:</label>
                            <textarea id="file-description" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="file-tags">Tags (comma separated):</label>
                            <input type="text" id="file-tags" placeholder="e.g., calculus, derivatives, integration">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Upload File
                        </button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Browse Section -->
        <section id="browse" class="section">
            <div class="container">
                <h2>Browse Notes</h2>
                
                <div class="filters">
                    <div class="filter-group">
                        <label for="subject-filter">Filter by Subject:</label>
                        <select id="subject-filter">
                            <option value="">All Subjects</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="type-filter">Filter by Type:</label>
                        <select id="type-filter">
                            <option value="">All Types</option>
                            <option value="pdf">PDF</option>
                            <option value="doc">Document</option>
                            <option value="ppt">Presentation</option>
                            <option value="txt">Text</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="sort-filter">Sort by:</label>
                        <select id="sort-filter">
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="title">Title A-Z</option>
                            <option value="subject">Subject</option>
                        </select>
                    </div>
                </div>
                
                <div id="files-grid" class="file-grid">
                    <!-- Files will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Search Section -->
        <section id="search" class="section">
            <div class="container">
                <h2>Search Notes</h2>
                
                <div class="search-form">
                    <div class="search-input-group">
                        <input type="text" id="search-input" placeholder="Search by title, subject, or tags...">
                        <button id="search-btn" class="btn btn-primary">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </div>
                
                <div id="search-results" class="file-grid">
                    <!-- Search results will be populated by JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <!-- File Preview Modal -->
    <div id="preview-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="preview-title"></h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="preview-content"></div>
                <div class="file-actions">
                    <button id="download-btn" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <div class="comments-section">
                    <h4>Comments</h4>
                    <div id="comments-list"></div>
                    <div class="add-comment">
                        <textarea id="comment-input" placeholder="Add a comment..."></textarea>
                        <button id="add-comment-btn" class="btn btn-secondary">
                            <i class="fas fa-comment"></i> Add Comment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container"></div>

    <script src="script.js"></script>
</body>
</html>
