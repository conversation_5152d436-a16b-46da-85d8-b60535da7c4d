// Global variables
let files = JSON.parse(localStorage.getItem('notesFiles')) || [];
let comments = JSON.parse(localStorage.getItem('notesComments')) || {};
let currentFileId = null;

// DOM elements
const navLinks = document.querySelectorAll('.nav-link');
const sections = document.querySelectorAll('.section');
const uploadArea = document.getElementById('upload-area');
const fileInput = document.getElementById('file-input');
const fileForm = document.getElementById('file-form');
const filesGrid = document.getElementById('files-grid');
const recentFiles = document.getElementById('recent-files');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const searchResults = document.getElementById('search-results');
const previewModal = document.getElementById('preview-modal');
const subjectFilter = document.getElementById('subject-filter');
const typeFilter = document.getElementById('type-filter');
const sortFilter = document.getElementById('sort-filter');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    updateStats();
    displayRecentFiles();
    displayAllFiles();
    populateSubjectFilter();
});

function initializeApp() {
    // Show home section by default
    showSection('home');
}

function setupEventListeners() {
    // Navigation
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.dataset.section;
            showSection(section);
            updateActiveNav(link);
        });
    });

    // Upload area
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    
    fileInput.addEventListener('change', handleFileSelect);
    fileForm.addEventListener('submit', handleFileUpload);

    // Search
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') performSearch();
    });

    // Filters
    subjectFilter.addEventListener('change', applyFilters);
    typeFilter.addEventListener('change', applyFilters);
    sortFilter.addEventListener('change', applyFilters);

    // Modal
    const modal = document.getElementById('preview-modal');
    const closeBtn = modal.querySelector('.close');
    closeBtn.addEventListener('click', closeModal);
    window.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });

    // Comments
    document.getElementById('add-comment-btn').addEventListener('click', addComment);
}

function showSection(sectionId) {
    sections.forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(sectionId).classList.add('active');
}

function updateActiveNav(activeLink) {
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFiles(droppedFiles);
}

function handleFileSelect(e) {
    const selectedFiles = Array.from(e.target.files);
    handleFiles(selectedFiles);
}

function handleFiles(fileList) {
    if (fileList.length > 0) {
        const file = fileList[0]; // Handle first file for demo
        document.getElementById('file-title').value = file.name.split('.')[0];
        showToast('File selected: ' + file.name, 'success');
    }
}

function handleFileUpload(e) {
    e.preventDefault();
    
    const title = document.getElementById('file-title').value;
    const subject = document.getElementById('file-subject').value;
    const description = document.getElementById('file-description').value;
    const tags = document.getElementById('file-tags').value;
    const fileInputElement = document.getElementById('file-input');
    
    if (!title || !subject || !fileInputElement.files[0]) {
        showToast('Please fill in all required fields and select a file', 'error');
        return;
    }

    const file = fileInputElement.files[0];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    
    const newFile = {
        id: generateId(),
        title: title,
        subject: subject,
        description: description,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        fileName: file.name,
        fileType: fileExtension,
        fileSize: file.size,
        uploadDate: new Date().toISOString(),
        fileData: null // In a real app, this would be uploaded to a server
    };

    // Read file as base64 for demo purposes (limited by browser storage)
    const reader = new FileReader();
    reader.onload = function(e) {
        newFile.fileData = e.target.result;
        files.push(newFile);
        saveToLocalStorage();
        showToast('File uploaded successfully!', 'success');
        fileForm.reset();
        updateStats();
        displayRecentFiles();
        displayAllFiles();
        populateSubjectFilter();
    };
    reader.readAsDataURL(file);
}

function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function saveToLocalStorage() {
    localStorage.setItem('notesFiles', JSON.stringify(files));
    localStorage.setItem('notesComments', JSON.stringify(comments));
}

function updateStats() {
    const totalFiles = files.length;
    const totalSubjects = new Set(files.map(file => file.subject)).size;
    const totalComments = Object.values(comments).reduce((sum, fileComments) => sum + fileComments.length, 0);
    
    document.getElementById('total-files').textContent = totalFiles;
    document.getElementById('total-subjects').textContent = totalSubjects;
    document.getElementById('total-comments').textContent = totalComments;
}

function displayRecentFiles() {
    const recent = files.slice(-6).reverse(); // Last 6 files
    recentFiles.innerHTML = recent.map(file => createFileCard(file)).join('');
}

function displayAllFiles() {
    filesGrid.innerHTML = files.map(file => createFileCard(file)).join('');
}

function createFileCard(file) {
    const fileIcon = getFileIcon(file.fileType);
    const uploadDate = new Date(file.uploadDate).toLocaleDateString();
    const fileSize = formatFileSize(file.fileSize);
    
    return `
        <div class="file-card" onclick="openFilePreview('${file.id}')">
            <div class="file-icon ${file.fileType}">
                <i class="${fileIcon}"></i>
            </div>
            <div class="file-title">${file.title}</div>
            <div class="file-subject">${file.subject}</div>
            <div class="file-description">${file.description || 'No description'}</div>
            <div class="file-tags">
                ${file.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
            <div class="file-meta">
                <span>${uploadDate}</span>
                <span>${fileSize}</span>
            </div>
        </div>
    `;
}

function getFileIcon(fileType) {
    const icons = {
        pdf: 'fas fa-file-pdf',
        doc: 'fas fa-file-word',
        docx: 'fas fa-file-word',
        ppt: 'fas fa-file-powerpoint',
        pptx: 'fas fa-file-powerpoint',
        txt: 'fas fa-file-alt',
        default: 'fas fa-file'
    };
    return icons[fileType] || icons.default;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function populateSubjectFilter() {
    const subjects = [...new Set(files.map(file => file.subject))];
    subjectFilter.innerHTML = '<option value="">All Subjects</option>' +
        subjects.map(subject => `<option value="${subject}">${subject}</option>`).join('');
}

function applyFilters() {
    const subjectValue = subjectFilter.value;
    const typeValue = typeFilter.value;
    const sortValue = sortFilter.value;
    
    let filteredFiles = [...files];
    
    // Apply subject filter
    if (subjectValue) {
        filteredFiles = filteredFiles.filter(file => file.subject === subjectValue);
    }
    
    // Apply type filter
    if (typeValue) {
        filteredFiles = filteredFiles.filter(file => file.fileType === typeValue);
    }
    
    // Apply sorting
    switch (sortValue) {
        case 'newest':
            filteredFiles.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));
            break;
        case 'oldest':
            filteredFiles.sort((a, b) => new Date(a.uploadDate) - new Date(b.uploadDate));
            break;
        case 'title':
            filteredFiles.sort((a, b) => a.title.localeCompare(b.title));
            break;
        case 'subject':
            filteredFiles.sort((a, b) => a.subject.localeCompare(b.subject));
            break;
    }
    
    filesGrid.innerHTML = filteredFiles.map(file => createFileCard(file)).join('');
}

function performSearch() {
    const query = searchInput.value.toLowerCase().trim();
    if (!query) {
        showToast('Please enter a search term', 'warning');
        return;
    }
    
    const results = files.filter(file => 
        file.title.toLowerCase().includes(query) ||
        file.subject.toLowerCase().includes(query) ||
        file.description.toLowerCase().includes(query) ||
        file.tags.some(tag => tag.toLowerCase().includes(query))
    );
    
    searchResults.innerHTML = results.length > 0 
        ? results.map(file => createFileCard(file)).join('')
        : '<p style="text-align: center; color: #666; padding: 2rem;">No files found matching your search.</p>';
    
    showToast(`Found ${results.length} file(s)`, 'success');
}

function openFilePreview(fileId) {
    const file = files.find(f => f.id === fileId);
    if (!file) return;
    
    currentFileId = fileId;
    document.getElementById('preview-title').textContent = file.title;
    
    const previewContent = document.getElementById('preview-content');
    previewContent.innerHTML = `
        <div style="text-align: center;">
            <i class="${getFileIcon(file.fileType)}" style="font-size: 4rem; color: #667eea; margin-bottom: 1rem;"></i>
            <h3>${file.title}</h3>
            <p><strong>Subject:</strong> ${file.subject}</p>
            <p><strong>Description:</strong> ${file.description || 'No description'}</p>
            <p><strong>File Type:</strong> ${file.fileType.toUpperCase()}</p>
            <p><strong>Size:</strong> ${formatFileSize(file.fileSize)}</p>
            <p><strong>Upload Date:</strong> ${new Date(file.uploadDate).toLocaleDateString()}</p>
            <div class="file-tags" style="justify-content: center; margin-top: 1rem;">
                ${file.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
        </div>
    `;
    
    // Setup download button
    document.getElementById('download-btn').onclick = () => downloadFile(file);
    
    // Load comments
    loadComments(fileId);
    
    previewModal.style.display = 'block';
}

function downloadFile(file) {
    if (file.fileData) {
        const link = document.createElement('a');
        link.href = file.fileData;
        link.download = file.fileName;
        link.click();
        showToast('Download started', 'success');
    } else {
        showToast('File data not available', 'error');
    }
}

function closeModal() {
    previewModal.style.display = 'none';
    currentFileId = null;
}

function loadComments(fileId) {
    const fileComments = comments[fileId] || [];
    const commentsList = document.getElementById('comments-list');
    
    commentsList.innerHTML = fileComments.length > 0
        ? fileComments.map(comment => `
            <div class="comment">
                <div class="comment-meta">Anonymous • ${new Date(comment.date).toLocaleDateString()}</div>
                <div>${comment.text}</div>
            </div>
        `).join('')
        : '<p style="color: #666; text-align: center;">No comments yet. Be the first to comment!</p>';
}

function addComment() {
    const commentInput = document.getElementById('comment-input');
    const commentText = commentInput.value.trim();
    
    if (!commentText) {
        showToast('Please enter a comment', 'warning');
        return;
    }
    
    if (!currentFileId) return;
    
    if (!comments[currentFileId]) {
        comments[currentFileId] = [];
    }
    
    comments[currentFileId].push({
        text: commentText,
        date: new Date().toISOString()
    });
    
    saveToLocalStorage();
    loadComments(currentFileId);
    commentInput.value = '';
    updateStats();
    showToast('Comment added successfully', 'success');
}

function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    document.getElementById('toast-container').appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}
